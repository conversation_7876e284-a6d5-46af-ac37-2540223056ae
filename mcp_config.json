{"mcpServers": {"mem0-openmemory": {"command": "python", "args": ["openmemory/api/main.py"], "cwd": "C:/Users/<USER>/mem0", "env": {"PYTHONPATH": "C:/Users/<USER>/mem0", "OPENAI_API_KEY": "sk-zxcvb1234567890qwertasdfg", "OPENAI_API_URL": "https://gemini-cli-worker-testing.13467879663.workers.dev/v1", "OPENAI_API_MODEL": "gemini-2.5-pro", "API_KEY": "sk-zxcvb1234567890qwertasdfg", "USER": "default_user", "POSTGRES_HOST": "localhost", "POSTGRES_PORT": "5432", "POSTGRES_DB": "openmemory", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "postgres", "POSTGRES_COLLECTION_NAME": "memories", "NEO4J_URI": "neo4j://localhost:7687", "NEO4J_USERNAME": "neo4j", "NEO4J_PASSWORD": "**********"}, "description": "mem0智能记忆系统 - 支持向量搜索和知识图谱的混合记忆存储", "capabilities": ["memory_management", "vector_search", "knowledge_graph", "semantic_search", "entity_extraction", "relationship_mapping", "crud_operations"], "version": "1.0.0", "author": "mem0 Team", "license": "MIT"}}, "tools": [{"name": "add_memory", "description": "添加新的记忆到mem0系统，支持自动实体提取和关系映射", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "要添加的记忆内容"}, "user_id": {"type": "string", "description": "用户ID，用于记忆隔离"}, "metadata": {"type": "object", "description": "可选的元数据信息", "properties": {"category": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "priority": {"type": "number"}}}}, "required": ["content", "user_id"]}, "returns": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}, "result": {"type": "object", "properties": {"results": {"type": "array", "description": "向量存储的记忆列表"}, "relations": {"type": "object", "description": "图数据库提取的实体关系"}}}}}}, {"name": "search_memory", "description": "在记忆中进行语义搜索，结合向量相似度和知识图谱", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "搜索查询文本"}, "user_id": {"type": "string", "description": "用户ID，用于记忆隔离"}, "limit": {"type": "integer", "description": "返回结果数量限制", "default": 5, "minimum": 1, "maximum": 50}, "threshold": {"type": "number", "description": "相似度阈值", "default": 0.0, "minimum": 0.0, "maximum": 1.0}}, "required": ["query", "user_id"]}, "returns": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}, "results": {"type": "object", "properties": {"results": {"type": "array", "description": "向量搜索结果"}, "relations": {"type": "array", "description": "图搜索关系结果"}}}}}}, {"name": "get_all_memories", "description": "获取用户的所有记忆", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "用户ID"}, "limit": {"type": "integer", "description": "返回记忆数量限制", "default": 100, "minimum": 1, "maximum": 1000}}, "required": ["user_id"]}, "returns": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}, "memories": {"type": "object", "properties": {"results": {"type": "array"}, "relations": {"type": "array"}}}}}}, {"name": "update_memory", "description": "更新指定的记忆内容", "parameters": {"type": "object", "properties": {"memory_id": {"type": "string", "description": "要更新的记忆ID"}, "content": {"type": "string", "description": "新的记忆内容"}}, "required": ["memory_id", "content"]}, "returns": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}}}}, {"name": "delete_memory", "description": "删除指定的记忆", "parameters": {"type": "object", "properties": {"memory_id": {"type": "string", "description": "要删除的记忆ID"}}, "required": ["memory_id"]}, "returns": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}}}}, {"name": "clear_all_memories", "description": "清除所有记忆数据（谨慎使用）", "parameters": {"type": "object", "properties": {"confirm": {"type": "boolean", "description": "确认清除所有数据"}}, "required": ["confirm"]}, "returns": {"type": "object", "properties": {"status": {"type": "string"}, "message": {"type": "string"}}}}], "resources": [{"name": "memory_statistics", "description": "记忆系统统计信息", "uri": "mem0://stats", "mimeType": "application/json"}, {"name": "system_health", "description": "系统健康状态检查", "uri": "mem0://health", "mimeType": "application/json"}, {"name": "configuration", "description": "系统配置信息", "uri": "mem0://config", "mimeType": "application/json"}], "prompts": [{"name": "memory_analysis", "description": "分析记忆内容并提取关键信息", "arguments": [{"name": "content", "description": "要分析的记忆内容", "required": true}]}, {"name": "knowledge_graph_query", "description": "基于知识图谱的复杂查询", "arguments": [{"name": "query", "description": "图查询语句", "required": true}, {"name": "user_id", "description": "用户ID", "required": true}]}]}