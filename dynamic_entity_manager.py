#!/usr/bin/env python3
"""
动态实体关系管理系统 - 基于Graphiti的动态类型处理
支持任意实体类型和关系类型，无需硬编码枚举
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 预定义的常见实体类型（可扩展）
COMMON_ENTITY_TYPES = {
    # 人员与组织
    "PERSON", "TEAM", "ROLE", "ORGANIZATION", "COMPANY",
    # 项目管理
    "PROJECT", "FEATURE", "TASK", "BUG", "MILESTONE", "RELEASE", "REQUIREMENT",
    # 代码与版本控制
    "REPOSITORY", "BRANCH", "COMMIT", "PULL_REQUEST", "CODE_FILE", "FUNCTION", "CLASS",
    # 技术栈
    "PROGRAMMING_LANGUAGE", "FRAMEWORK", "LIBRARY", "DATABASE", "PLATFORM", "TOOL",
    # 基础设施
    "ENVIRONMENT", "SERVER", "CLOUD_SERVICE", "CONTAINER", "DEPLOYMENT",
    # 测试与质量
    "TEST_CASE", "TEST_SUITE", "BUG_REPORT", "SECURITY_VULNERABILITY",
    # 文档
    "DOCUMENT", "API_SPECIFICATION", "MEETING_NOTES"
}

# 预定义的常见关系类型（可扩展）
COMMON_RELATION_TYPES = {
    # 人员与组织关系
    "MEMBER_OF", "WORKS_FOR", "MANAGES", "COLLABORATES_WITH", "REPORTS_TO",
    # 项目管理关系
    "PART_OF", "BELONGS_TO", "ASSIGNED_TO", "BLOCKS", "DEPENDS_ON",
    # 代码与开发关系
    "AUTHORED_BY", "REVIEWED_BY", "IMPLEMENTS", "FIXES", "MODIFIES", "CONTAINS",
    # 技术关系
    "USES", "INTEGRATES_WITH", "EXTENDS", "CALLS", "INHERITS_FROM",
    # 基础设施关系
    "DEPLOYED_TO", "HOSTED_ON", "RUNS_IN", "TRIGGERED_BY",
    # 测试关系
    "TESTS", "FOUND_BY", "DETECTED",
    # 文档关系
    "DOCUMENTS", "REFERENCES", "CREATED_BY",
    # 通用关系
    "IS_A", "RELATES_TO", "SKILLED_IN", "EXPERT_IN", "IDENTIFIES_AS", "RESPONSIBLE_FOR"
}

@dataclass
class DynamicEntity:
    """动态实体数据类"""
    name: str
    entity_type: str  # 动态字符串类型
    properties: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    confidence: float = 1.0
    
@dataclass
class DynamicRelation:
    """动态关系数据类"""
    source_entity: str
    target_entity: str
    relation_type: str  # 动态字符串类型
    properties: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    valid_from: datetime
    confidence: float = 1.0
    valid_to: Optional[datetime] = None

@dataclass
class DynamicEpisode:
    """动态情节数据类"""
    content: str
    episode_id: str
    user_id: str
    created_at: datetime
    entities: List[DynamicEntity]
    relations: List[DynamicRelation]
    metadata: Dict[str, Any]

class DynamicEntityManager:
    """动态实体关系管理器 - 支持任意类型"""
    
    def __init__(self, memory_client):
        self.memory = memory_client
        self.logger = logging.getLogger(__name__)
        
    async def process_episode(self, content: str, user_id: str, metadata: Optional[Dict] = None) -> DynamicEpisode:
        """
        处理情节 - 动态提取实体关系
        """
        episode_id = f"episode_{datetime.now().isoformat()}_{user_id}"
        created_at = datetime.now()
        
        try:
            # 动态提取实体和关系
            entities, relations = await self._extract_entities_and_relations_dynamic(content, user_id)
            
            # 创建情节对象
            episode = DynamicEpisode(
                content=content,
                episode_id=episode_id,
                user_id=user_id,
                created_at=created_at,
                entities=entities,
                relations=relations,
                metadata=metadata or {}
            )
            
            self.logger.info(f"动态情节处理完成: {episode_id}, 实体: {len(entities)}, 关系: {len(relations)}")
            return episode
            
        except Exception as e:
            self.logger.error(f"动态情节处理失败: {e}")
            # 返回空的情节对象而不是抛出异常
            return DynamicEpisode(
                content=content,
                episode_id=episode_id,
                user_id=user_id,
                created_at=created_at,
                entities=[],
                relations=[],
                metadata=metadata or {}
            )
    
    async def _extract_entities_and_relations_dynamic(self, content: str, user_id: str) -> Tuple[List[DynamicEntity], List[DynamicRelation]]:
        """
        动态提取实体和关系 - 不限制类型
        """
        entities = []
        relations = []
        
        try:
            # 构建动态提取提示词
            extraction_prompt = self._build_dynamic_extraction_prompt(content)
            
            # 调用LLM进行实体提取
            if hasattr(self.memory, 'llm') and self.memory.llm:
                llm_response = self.memory.llm.generate_response(
                    messages=[{"role": "user", "content": extraction_prompt}]
                )
                
                # 调试：打印LLM响应
                self.logger.info(f"LLM响应: {llm_response}")
                
                # 解析LLM响应
                extraction_result = self._parse_dynamic_extraction_result(llm_response)
                entities = extraction_result.get('entities', [])
                relations = extraction_result.get('relations', [])
                
        except Exception as e:
            self.logger.error(f"动态实体提取失败: {e}")
            
        return entities, relations
    
    def _build_dynamic_extraction_prompt(self, content: str) -> str:
        """构建动态实体提取提示词"""
        common_entities = ", ".join(sorted(list(COMMON_ENTITY_TYPES)[:15]))  # 只显示前15个
        common_relations = ", ".join(sorted(list(COMMON_RELATION_TYPES)[:15]))  # 只显示前15个
        
        return f"""
你是一个专业的知识图谱构建专家。请从以下文本中提取实体和关系。

文本内容: "{content}"

请提取以下信息并以JSON格式返回：

{{
  "entities": [
    {{
      "name": "实体名称",
      "type": "实体类型",
      "properties": {{"description": "实体描述"}},
      "confidence": 0.95
    }}
  ],
  "relations": [
    {{
      "source": "源实体名称",
      "target": "目标实体名称", 
      "type": "关系类型",
      "properties": {{"description": "关系描述"}},
      "confidence": 0.90
    }}
  ]
}}

重要指导原则：
1. 常见实体类型：{common_entities}
2. 常见关系类型：{common_relations}
3. 可以创建新的类型（使用大写字母和下划线）
4. 对于"我"使用"USER_ID"作为实体名称
5. 确保实体名称一致性
6. 置信度反映确定性程度

只返回JSON，不要其他解释。
        """
    
    def _parse_dynamic_extraction_result(self, llm_response: str) -> Dict[str, List]:
        """解析动态LLM提取结果"""
        try:
            import json
            
            # 处理markdown代码块包装的JSON
            if llm_response.startswith('```json'):
                json_start = llm_response.find('{')
                json_end = llm_response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    llm_response = llm_response[json_start:json_end]
            
            result = json.loads(llm_response)
            
            # 转换为我们的动态数据结构
            entities = []
            for entity_data in result.get('entities', []):
                # 验证和标准化实体类型
                entity_type = self._normalize_entity_type(entity_data.get('type', 'ENTITY'))
                
                entity = DynamicEntity(
                    name=entity_data['name'],
                    entity_type=entity_type,
                    properties=entity_data.get('properties', {}),
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    confidence=entity_data.get('confidence', 1.0)
                )
                entities.append(entity)
            
            relations = []
            for relation_data in result.get('relations', []):
                # 验证和标准化关系类型
                relation_type = self._normalize_relation_type(relation_data.get('type', 'RELATES_TO'))
                
                relation = DynamicRelation(
                    source_entity=relation_data['source'],
                    target_entity=relation_data['target'],
                    relation_type=relation_type,
                    properties=relation_data.get('properties', {}),
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    confidence=relation_data.get('confidence', 1.0),
                    valid_from=datetime.now()
                )
                relations.append(relation)
                
            return {'entities': entities, 'relations': relations}
            
        except Exception as e:
            self.logger.error(f"解析动态提取结果失败: {e}")
            return {'entities': [], 'relations': []}
    
    def _normalize_entity_type(self, entity_type: str) -> str:
        """标准化实体类型"""
        if not entity_type:
            return "ENTITY"
        
        # 转换为大写并用下划线分隔
        normalized = entity_type.upper().replace(' ', '_').replace('-', '_')
        
        # 如果是常见类型，直接返回
        if normalized in COMMON_ENTITY_TYPES:
            return normalized
        
        # 否则返回标准化后的类型（允许新类型）
        return normalized
    
    def _normalize_relation_type(self, relation_type: str) -> str:
        """标准化关系类型"""
        if not relation_type:
            return "RELATES_TO"
        
        # 转换为大写并用下划线分隔
        normalized = relation_type.upper().replace(' ', '_').replace('-', '_')
        
        # 如果是常见类型，直接返回
        if normalized in COMMON_RELATION_TYPES:
            return normalized
        
        # 否则返回标准化后的类型（允许新类型）
        return normalized
    
    async def hybrid_search(self, query: str, user_id: str, limit: int = 10) -> Dict[str, Any]:
        """
        动态混合搜索 - 结合向量搜索和图遍历
        """
        try:
            # 1. 向量语义搜索
            vector_results = await self.memory.search(query, user_id=user_id, limit=limit*2)
            
            # 2. 图数据库搜索
            graph_results = []
            if hasattr(self.memory, 'graph') and self.memory.graph:
                filters = {'user_id': user_id}
                graph_results = self.memory.graph.search(query, filters=filters, limit=limit)
            
            # 3. 结合和重排序结果
            combined_results = self._combine_search_results(vector_results, graph_results, query)
            
            return {
                'status': 'success',
                'results': combined_results[:limit],
                'vector_count': len(vector_results.get('results', [])),
                'graph_count': len(graph_results),
                'total_count': len(combined_results)
            }
            
        except Exception as e:
            self.logger.error(f"动态混合搜索失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'results': []
            }
    
    def _combine_search_results(self, vector_results: Dict, graph_results: List, query: str) -> List[Dict]:
        """结合向量和图搜索结果"""
        combined = []
        
        # 添加向量搜索结果
        if isinstance(vector_results, dict) and 'results' in vector_results:
            for result in vector_results['results']:
                result['source'] = 'vector'
                combined.append(result)
        
        # 添加图搜索结果
        for result in graph_results:
            result['source'] = 'graph'
            combined.append(result)
        
        # 简单的重排序（可以进一步优化）
        combined.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return combined
