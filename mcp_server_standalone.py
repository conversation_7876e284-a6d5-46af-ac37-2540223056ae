#!/usr/bin/env python3
"""
独立的OpenMemory MCP服务器
专门为Claude Desktop等MCP客户端设计
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# 设置环境变量
os.environ.setdefault("PYTHONPATH", "C:/Users/<USER>/mem0")
os.environ.setdefault("OPENAI_API_KEY", "1")
os.environ.setdefault("OPENAI_API_URL", "http://127.0.0.1:8001/v1")
os.environ.setdefault("OPENAI_API_MODEL", "gemini-2.5-flash")
os.environ.setdefault("POSTGRES_HOST", "localhost")
os.environ.setdefault("POSTGRES_PORT", "5432")
os.environ.setdefault("POSTGRES_DB", "openmemory")
os.environ.setdefault("POSTGRES_USER", "postgres")
os.environ.setdefault("POSTGRES_PASSWORD", "1513091437")
os.environ.setdefault("NEO4J_URI", "neo4j://localhost:7687")
os.environ.setdefault("NEO4J_USERNAME", "neo4j")
os.environ.setdefault("NEO4J_PASSWORD", "1513091437")
os.environ.setdefault("USER", "default_user")

# 添加项目路径到Python路径
sys.path.insert(0, "C:/Users/<USER>/mem0")

try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
    import mcp.types as types
except ImportError as e:
    print(f"Error importing MCP: {e}", file=sys.stderr)
    print("Please install mcp package: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 导入格式化工具（安全导入）
try:
    from format_utils import format_memory_result
    USE_FORMATTING = True
except ImportError as e:
    logger.warning(f"格式化工具导入失败，使用默认格式: {e}")
    USE_FORMATTING = False

    def format_memory_result(result, operation_type):
        """备用格式化函数"""
        import json
        return json.dumps(result, ensure_ascii=False, indent=2)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建MCP服务器
server = Server("openmemory")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="add_memories",
            description="添加新的编程相关记忆到OpenMemory系统（自动进行实体提取和关系识别）。使用场合：1)记录编程项目信息和技术栈 2)保存开发经验和学习内容 3)记录团队协作和项目进展。系统特点：自动提取技术实体关系，构建编程知识图谱，智能合并相似记忆。使用方法：提供编程相关的完整描述，系统会自动处理并提取技术实体关系。",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "要添加的记忆内容"
                    }
                },
                "required": ["text"]
            }
        ),

        Tool(
            name="list_memories",
            description="列出用户记忆中的所有记忆",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="delete_all_memories",
            description="删除用户记忆中的所有记忆",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="delete_memory",
            description="删除指定ID的单个记忆",
            inputSchema={
                "type": "object",
                "properties": {
                    "memory_id": {
                        "type": "string",
                        "description": "要删除的记忆ID"
                    }
                },
                "required": ["memory_id"]
            }
        ),
        Tool(
            name="get_memory",
            description="获取指定ID的单个记忆详情。使用场合：1)查看记忆的完整信息和元数据 2)确认记忆内容后再进行更新或删除 3)调试时检查记忆状态。使用方法：先用list_memories获取记忆列表和ID，然后用完整的UUID调用此工具。",
            inputSchema={
                "type": "object",
                "properties": {
                    "memory_id": {
                        "type": "string",
                        "description": "要获取的记忆ID，必须是完整的UUID格式，如：a1b2c3d4-e5f6-7890-abcd-ef1234567890"
                    }
                },
                "required": ["memory_id"]
            }
        ),
        Tool(
            name="update_memory",
            description="更新指定ID的记忆内容。使用场合：1)修正记忆中的错误信息 2)更新过时的记忆内容 3)补充记忆的详细信息。注意：mem0有自动合并机制，添加相似记忆时会智能合并，但如需精确修改特定记忆请使用此工具。使用方法：先用get_memory确认要修改的记忆，然后提供新内容进行更新。",
            inputSchema={
                "type": "object",
                "properties": {
                    "memory_id": {
                        "type": "string",
                        "description": "要更新的记忆ID，必须是完整的UUID格式"
                    },
                    "data": {
                        "type": "string",
                        "description": "新的记忆内容，将完全替换原有内容"
                    }
                },
                "required": ["memory_id", "data"]
            }
        ),
        Tool(
            name="get_entity_relations",
            description="获取指定编程实体的关系网络和相关记忆。分析编程项目中实体之间的技术关系。使用场合：1)分析技术栈和框架的依赖关系 2)查看项目组件和团队成员的协作关系 3)构建以技术实体为中心的编程知识图谱。使用方法：提供编程相关的实体名称（如技术名称、项目名称、开发者姓名等），系统返回该实体的相关记忆和技术关系网络。",
            inputSchema={
                "type": "object",
                "properties": {
                    "entity_name": {
                        "type": "string",
                        "description": "要分析的实体名称，如人名、地点、组织名等"
                    }
                },
                "required": ["entity_name"]
            }
        ),


        Tool(
            name="hybrid_search",
            description="基于关键词搜索存储的记忆和实体关系。使用场合：1)根据关键词查找相关记忆 2)搜索特定主题的内容 3)查找包含特定实体的记忆。需要提供搜索关键词作为参数。系统结合向量搜索和图遍历，返回相关记忆列表。",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询内容"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回结果数量限制",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    try:
        # 导入我们的应用函数
        from app import add_memory, get_all_memories, delete_all_memories, delete_memory, get_memory, update_memory, get_entity_relations
        
        user_id = "default_user"
        
        if name == "add_memories":
            text = arguments.get("text", "")
            if not text:
                return [TextContent(type="text", text="错误: 缺少记忆内容")]
            
            result = await add_memory(text, user_id=user_id)
            formatted_result = format_memory_result(result, "add")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "search_memory":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]
            
            result = await search_memory(query, user_id=user_id, limit=5)
            formatted_result = format_memory_result(result, "search")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "list_memories":
            result = await get_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "list")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "delete_all_memories":
            result = await delete_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "delete_all")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "delete_memory":
            memory_id = arguments.get("memory_id", "")
            if not memory_id:
                return [TextContent(type="text", text="错误: 缺少记忆ID")]

            result = await delete_memory(memory_id=memory_id, user_id=user_id)
            formatted_result = format_memory_result(result, "delete_single")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_memory":
            memory_id = arguments.get("memory_id", "")
            if not memory_id:
                return [TextContent(type="text", text="错误: 缺少记忆ID")]

            result = await get_memory(memory_id=memory_id, user_id=user_id)
            formatted_result = format_memory_result(result, "get")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "update_memory":
            memory_id = arguments.get("memory_id", "")
            data = arguments.get("data", "")
            if not memory_id:
                return [TextContent(type="text", text="错误: 缺少记忆ID")]
            if not data:
                return [TextContent(type="text", text="错误: 缺少更新内容")]

            result = await update_memory(memory_id=memory_id, data=data, user_id=user_id)
            formatted_result = format_memory_result(result, "update")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_entity_relations":
            entity_name = arguments.get("entity_name", "")
            if not entity_name:
                return [TextContent(type="text", text="错误: 缺少实体名称")]

            result = await get_entity_relations(entity_name=entity_name, user_id=user_id)
            formatted_result = format_memory_result(result, "entity_relations")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "hybrid_search":
            query = arguments.get("query", "")
            limit = arguments.get("limit", 10)
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]

            # 使用动态混合搜索
            from dynamic_entity_manager import DynamicEntityManager
            from app import get_memory_client

            memory = await get_memory_client()
            entity_manager = DynamicEntityManager(memory)
            result = await entity_manager.hybrid_search(query, user_id or "default_user", limit)
            formatted_result = format_memory_result(result, "hybrid_search")
            return [TextContent(type="text", text=formatted_result)]

        else:
            return [TextContent(type="text", text=f"未知工具: {name}")]
    
    except Exception as e:
        logger.error(f"工具调用失败 {name}: {e}")
        return [TextContent(type="text", text=f"工具调用失败: {e}")]

async def main():
    """主函数"""
    logger.info("启动OpenMemory MCP服务器...")
    
    # 检查依赖
    try:
        from app import add_memory
        logger.info("✓ 应用模块导入成功")
    except Exception as e:
        logger.error(f"✗ 应用模块导入失败: {e}")
        return
    
    # 启动服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
